#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实API测试 - 获取订单列表接口
"""

import json
import time
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from request_utils import 获取订单列表
    print("✅ 成功导入 获取订单列表 函数")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_real_api():
    """进行真实的API测试"""
    print("\n" + "=" * 60)
    print("🚀 开始真实API测试")
    print("=" * 60)
    
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 测试目标: 获取订单列表接口")
    print("🌐 API地址: https://api.goofish.pro/api/order/pager")
    
    print("\n📋 测试参数:")
    print("  - time_type: 1")
    print("  - time[]: 2024-12-17 到 2025-03-17")
    print("  - idx: 1 (页码)")
    print("  - size: 25 (每页数量)")
    
    print("\n🔑 认证信息:")
    print("  - 使用JWT authorization token")
    print("  - 包含x-content-security签名")
    
    print("\n" + "-" * 60)
    print("🔄 正在发送请求...")
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 调用真实API
        print("📡 调用 获取订单列表() 函数...")
        获取订单列表()
        
        # 记录结束时间
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"\n⏱️  响应时间: {response_time:.3f} 秒")
        print("✅ API调用完成")
        
    except Exception as e:
        print(f"\n❌ API调用失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 分析可能的错误原因
        error_msg = str(e).lower()
        print("\n🔍 错误分析:")
        
        if "connection" in error_msg or "network" in error_msg:
            print("  - 可能是网络连接问题")
            print("  - 建议检查网络连接和防火墙设置")
        elif "timeout" in error_msg:
            print("  - 请求超时")
            print("  - 建议检查网络速度或增加超时时间")
        elif "401" in error_msg or "unauthorized" in error_msg:
            print("  - 认证失败，token可能已过期")
            print("  - 建议更新authorization token")
        elif "403" in error_msg or "forbidden" in error_msg:
            print("  - 访问被拒绝")
            print("  - 建议检查权限和签名")
        elif "404" in error_msg:
            print("  - API端点不存在")
            print("  - 建议检查URL是否正确")
        elif "500" in error_msg:
            print("  - 服务器内部错误")
            print("  - 建议稍后重试")
        else:
            print("  - 未知错误，建议检查网络和参数")
        
        return False
    
    return True

def analyze_token():
    """分析JWT token信息"""
    print("\n" + "=" * 60)
    print("🔍 JWT Token 分析")
    print("=" * 60)
    
    # 从request_utils.py中提取token
    token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NDIxNzQ0MDAsIm5iZiI6MTc0MjE3NDQwMCwiZXhwIjoxNzQyNzc5MjAwLCJzaWQiOjk4Mjk2MTM3MDI2NzcxNywidWlkIjo5ODI5NjEzNzAyNjc3MTgsIm5hbWUiOiJcdTVjMGZcdTk2NDhcdTY3NDJcdThkMjdcdTk0ZmFcdTk1ZjJcdTdiYTFcdTViYjYifQ.Pw6L9-frq8T_qDucnMidrtBj-kD4K25W0snDnZKRQZA"
    
    try:
        import base64
        
        # 分割JWT token
        parts = token.split('.')
        if len(parts) != 3:
            print("❌ JWT token格式不正确")
            return
        
        # 解码header
        header_data = parts[0] + '=' * (4 - len(parts[0]) % 4)  # 添加padding
        header = json.loads(base64.urlsafe_b64decode(header_data))
        print("📋 Token Header:")
        print(f"  - 类型: {header.get('typ', 'N/A')}")
        print(f"  - 算法: {header.get('alg', 'N/A')}")
        
        # 解码payload
        payload_data = parts[1] + '=' * (4 - len(parts[1]) % 4)  # 添加padding
        payload = json.loads(base64.urlsafe_b64decode(payload_data))
        print("\n📋 Token Payload:")
        
        # 转换时间戳
        if 'iat' in payload:
            iat_time = datetime.fromtimestamp(payload['iat'])
            print(f"  - 签发时间 (iat): {iat_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if 'nbf' in payload:
            nbf_time = datetime.fromtimestamp(payload['nbf'])
            print(f"  - 生效时间 (nbf): {nbf_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if 'exp' in payload:
            exp_time = datetime.fromtimestamp(payload['exp'])
            current_time = datetime.now()
            print(f"  - 过期时间 (exp): {exp_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if current_time > exp_time:
                print("  ⚠️  Token已过期!")
            else:
                remaining = exp_time - current_time
                print(f"  ✅ Token仍有效，剩余时间: {remaining}")
        
        if 'sid' in payload:
            print(f"  - 会话ID (sid): {payload['sid']}")
        
        if 'uid' in payload:
            print(f"  - 用户ID (uid): {payload['uid']}")
        
        if 'name' in payload:
            # 解码Unicode字符
            name = payload['name']
            if '\\u' in name:
                name = name.encode().decode('unicode_escape')
            print(f"  - 用户名 (name): {name}")
        
    except Exception as e:
        print(f"❌ Token解析失败: {e}")

def main():
    """主函数"""
    print("🧪 闲鱼获取订单列表接口 - 真实API测试")
    print("=" * 60)
    
    # 分析token
    analyze_token()
    
    # 询问是否继续
    print("\n" + "=" * 60)
    print("⚠️  注意事项:")
    print("1. 此测试将发送真实的HTTP请求到闲鱼API")
    print("2. 请确保网络连接正常")
    print("3. 如果token已过期，测试可能会失败")
    print("4. 测试结果将显示实际的API响应")
    
    try:
        user_input = input("\n是否继续进行真实API测试? (y/N): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            print("❌ 用户取消测试")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断测试")
        return
    
    # 进行真实API测试
    success = test_real_api()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 真实API测试完成!")
    else:
        print("⚠️  真实API测试遇到问题")
    
    print("📝 测试建议:")
    print("1. 如果token过期，请更新authorization字段")
    print("2. 如果网络错误，请检查网络连接")
    print("3. 如果权限错误，请检查x-content-security签名")
    print("=" * 60)

if __name__ == '__main__':
    main()
