#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终真实API测试 - 验证更新后的获取订单列表接口
"""

import sys
import os
import json
import base64
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🚀 最终真实API测试 - 更新后的获取订单列表接口")
print("=" * 70)

def analyze_current_token():
    """分析当前使用的JWT token"""
    print("🔍 当前Token分析:")
    
    # 当前代码中使用的token (来自标头.txt)
    current_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ0ODE3OTYsImlhdCI6MTc1Mzg3Njk5NiwibmFtZSI6Ikx1Y2tlcl9ZYW4iLCJzaWQiOjY5NzQzNDUwMjg0MDM4OSwidHlwZSI6MSwidWlkIjo2OTc0MzQ1MDI4NDAzOTB9.lJed8-xhejWvCBzcfv6pqIhtMx4UCIUNzJdR2DL_BWU"
    
    try:
        # 解码JWT token
        parts = current_token.split('.')
        if len(parts) != 3:
            print("❌ JWT token格式不正确")
            return False
        
        # 解码payload
        payload_data = parts[1] + '=' * (4 - len(parts[1]) % 4)
        payload = json.loads(base64.urlsafe_b64decode(payload_data))
        
        print("📋 Token详情:")
        
        if 'iat' in payload:
            iat_time = datetime.fromtimestamp(payload['iat'])
            print(f"  - 签发时间: {iat_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if 'exp' in payload:
            exp_time = datetime.fromtimestamp(payload['exp'])
            current_time = datetime.now()
            print(f"  - 过期时间: {exp_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if current_time > exp_time:
                print("  ⚠️  Token已过期!")
                time_diff = current_time - exp_time
                print(f"  - 过期时长: {time_diff}")
                return False
            else:
                remaining = exp_time - current_time
                print(f"  ✅ Token有效，剩余: {remaining}")
                return True
        
        if 'name' in payload:
            print(f"  - 用户名: {payload['name']}")
        
        if 'sid' in payload:
            print(f"  - 会话ID: {payload['sid']}")
        
        if 'uid' in payload:
            print(f"  - 用户ID: {payload['uid']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token解析失败: {e}")
        return False

def show_request_details():
    """显示请求详情"""
    print("\n📡 请求详情:")
    print("-" * 50)
    print("🌐 URL: https://api.goofish.pro/api/order/pager?channel=1&version=3.54.31")
    print("📝 方法: POST")
    print("📦 数据格式: JSON")
    print("🔑 认证: JWT Token + x-content-security签名")
    
    print("\n📋 请求数据:")
    data = {
        "time_type": 1,
        "time[]": "2024-12-17",
        "time[]": "2025-03-17",
        "idx": 1,
        "size": 25,
    }
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    print("\n🔐 关键请求头:")
    print("  - Authorization: JWT Token (来自标头.txt)")
    print("  - x-content-security: 最新签名")
    print("  - Content-Type: application/json;charset=UTF-8")
    print("  - Origin: https://www.goofish.pro")

def perform_real_api_test():
    """执行真实API测试"""
    print("\n🚀 开始真实API测试:")
    print("=" * 50)
    
    try:
        from request_utils import 获取订单列表
        print("✅ 成功导入更新后的函数")
        
        print(f"\n⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 记录开始时间
        start_time = time.time()
        
        print("\n📡 正在发送POST请求...")
        print("-" * 30)
        print("API响应:")
        print("-" * 30)
        
        # 调用更新后的API
        获取订单列表()
        
        # 记录结束时间
        end_time = time.time()
        response_time = end_time - start_time
        
        print("-" * 30)
        print(f"⏱️  响应时间: {response_time:.3f} 秒")
        print("✅ API调用完成")
        
        return True, response_time
        
    except Exception as e:
        print(f"\n❌ API调用失败: {e}")
        print(f"🔍 错误类型: {type(e).__name__}")
        
        # 详细错误分析
        error_msg = str(e).lower()
        print("\n🔍 错误分析:")
        
        if "connection" in error_msg or "network" in error_msg:
            print("  📡 网络连接问题")
            print("     - 检查网络连接")
            print("     - 检查防火墙设置")
            print("     - 检查代理配置")
        elif "timeout" in error_msg:
            print("  ⏰ 请求超时")
            print("     - 网络速度可能较慢")
            print("     - 服务器响应时间过长")
        elif "401" in error_msg or "unauthorized" in error_msg:
            print("  🔐 认证失败")
            print("     - Token可能已过期")
            print("     - 需要重新获取authorization")
        elif "403" in error_msg or "forbidden" in error_msg:
            print("  🚫 访问被拒绝")
            print("     - 权限不足")
            print("     - x-content-security签名可能错误")
        elif "400" in error_msg or "bad request" in error_msg:
            print("  📝 请求格式错误")
            print("     - 检查请求参数")
            print("     - 检查JSON格式")
        elif "404" in error_msg:
            print("  🔍 API端点不存在")
            print("     - 检查URL是否正确")
            print("     - 检查API版本")
        elif "500" in error_msg:
            print("  🔧 服务器内部错误")
            print("     - 服务器端问题")
            print("     - 稍后重试")
        else:
            print("  ❓ 未知错误")
            print("     - 检查所有配置")
            print("     - 查看详细错误信息")
        
        return False, 0

def analyze_response_status(success, response_time):
    """分析响应状态"""
    print("\n📊 测试结果分析:")
    print("=" * 50)
    
    if success:
        print("✅ 基本测试状态: 成功")
        print(f"⏱️  响应时间: {response_time:.3f} 秒")
        
        if response_time < 2.0:
            print("🚀 响应速度: 优秀")
        elif response_time < 5.0:
            print("👍 响应速度: 良好")
        elif response_time < 10.0:
            print("⚠️  响应速度: 一般")
        else:
            print("🐌 响应速度: 较慢")
        
        print("\n🔍 API响应分析:")
        print("  - 如果返回订单数据: 接口完全正常")
        print("  - 如果返回错误代码: 可能需要更新认证信息")
        print("  - 状态码1014: 通常表示安全验证问题")
        
    else:
        print("❌ 基本测试状态: 失败")
        print("🔧 需要检查:")
        print("  1. 网络连接")
        print("  2. Token有效性")
        print("  3. 签名正确性")
        print("  4. 请求格式")

def main():
    """主函数"""
    print("📝 测试目标: 验证根据标头.txt更新后的接口是否正常工作")
    
    # 1. 分析当前token
    token_valid = analyze_current_token()
    
    # 2. 显示请求详情
    show_request_details()
    
    # 3. 执行真实API测试
    success, response_time = perform_real_api_test()
    
    # 4. 分析测试结果
    analyze_response_status(success, response_time)
    
    # 5. 总结
    print("\n" + "=" * 70)
    print("📋 最终测试总结:")
    print("=" * 70)
    
    print("🔧 接口更新状态:")
    print("  ✅ 请求方法: GET → POST")
    print("  ✅ URL参数: 添加 channel=1&version=3.54.31")
    print("  ✅ 数据格式: URL参数 → JSON")
    print("  ✅ Token: 更新为标头.txt中的最新版本")
    print("  ✅ 请求头: 完全同步标头.txt")
    print("  ✅ 签名: 更新x-content-security")
    
    print(f"\n🧪 测试结果:")
    if token_valid:
        print("  ✅ Token状态: 有效")
    else:
        print("  ⚠️  Token状态: 可能过期")
    
    if success:
        print("  ✅ API调用: 成功")
        print("  ✅ 网络连接: 正常")
        print("  ✅ 请求格式: 正确")
    else:
        print("  ❌ API调用: 失败")
        print("  ⚠️  需要进一步调试")
    
    print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n💡 建议:")
    if success and token_valid:
        print("  🎉 接口更新成功，可以正常使用")
        print("  📅 建议定期更新token和签名")
    elif success and not token_valid:
        print("  ⚠️  虽然调用成功，但token可能需要更新")
    else:
        print("  🔧 需要检查网络、认证或请求格式")
        print("  📖 参考错误分析进行调试")
    
    print("=" * 70)

if __name__ == '__main__':
    main()
