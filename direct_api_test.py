#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接进行真实API测试
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🧪 闲鱼获取订单列表接口 - 真实API测试")
print("=" * 60)

# 分析JWT token
print("🔍 JWT Token 分析:")
token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NDIxNzQ0MDAsIm5iZiI6MTc0MjE3NDQwMCwiZXhwIjoxNzQyNzc5MjAwLCJzaWQiOjk4Mjk2MTM3MDI2NzcxNywidWlkIjo5ODI5NjEzNzAyNjc3MTgsIm5hbWUiOiJcdTVjMGZcdTk2NDhcdTY3NDJcdThkMjdcdTk0ZmFcdTk1ZjJcdTdiYTFcdTViYjYifQ.Pw6L9-frq8T_qDucnMidrtBj-kD4K25W0snDnZKRQZA"

try:
    import base64
    import json
    
    parts = token.split('.')
    payload_data = parts[1] + '=' * (4 - len(parts[1]) % 4)
    payload = json.loads(base64.urlsafe_b64decode(payload_data))
    
    if 'exp' in payload:
        exp_time = datetime.fromtimestamp(payload['exp'])
        current_time = datetime.now()
        print(f"  - 过期时间: {exp_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if current_time > exp_time:
            print("  ⚠️  Token已过期!")
        else:
            print("  ✅ Token仍有效")
            
    if 'name' in payload:
        name = payload['name']
        if '\\u' in name:
            name = name.encode().decode('unicode_escape')
        print(f"  - 用户名: {name}")
        
except Exception as e:
    print(f"❌ Token解析失败: {e}")

print("\n" + "=" * 60)
print("🚀 开始真实API测试")
print("=" * 60)

try:
    from request_utils import 获取订单列表
    print("✅ 成功导入函数")
    
    print("\n📡 正在调用真实API...")
    print("URL: https://api.goofish.pro/api/order/pager")
    print("方法: GET")
    print("参数: time_type=1, 时间范围=2024-12-17到2025-03-17")
    
    print("\n" + "-" * 40)
    print("API响应:")
    print("-" * 40)
    
    # 调用真实API
    获取订单列表()
    
    print("-" * 40)
    print("✅ API调用完成")
    
except Exception as e:
    print(f"\n❌ API调用失败: {e}")
    print(f"错误类型: {type(e).__name__}")
    
    # 错误分析
    error_msg = str(e).lower()
    print("\n🔍 可能的原因:")
    
    if "401" in error_msg or "unauthorized" in error_msg:
        print("  - Token已过期，需要重新获取authorization")
    elif "403" in error_msg or "forbidden" in error_msg:
        print("  - 权限不足或签名错误")
    elif "connection" in error_msg:
        print("  - 网络连接问题")
    elif "timeout" in error_msg:
        print("  - 请求超时")
    else:
        print("  - 其他网络或服务器问题")

print("\n" + "=" * 60)
print("📝 测试总结:")
print("1. Token状态: 已过期 (2025-03-24)")
print("2. 如需正常使用，请更新authorization token")
print("3. 建议从浏览器开发者工具获取最新token")
print("=" * 60)
