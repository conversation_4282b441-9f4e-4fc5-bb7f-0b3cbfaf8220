#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试获取订单列表接口
只测试 request_utils.py 中的 获取订单列表() 函数
"""

import unittest
import json
import requests
from unittest.mock import patch, Mock
from request_utils import 获取订单列表


class TestOrderList(unittest.TestCase):
    """测试获取订单列表接口"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.expected_url = 'https://api.goofish.pro/api/order/pager'
        self.expected_params = {
            "time_type": 1,
            "time[]": "2024-12-17",
            "time[]": "2025-03-17",
            "idx": 1,
            "size": 25,
        }
    
    @patch('requests.session')
    def test_获取订单列表_成功响应(self, mock_session):
        """测试获取订单列表成功的情况"""
        # 模拟成功的响应
        mock_response = Mock()
        mock_response.json.return_value = {
            "code": 200,
            "message": "success",
            "data": {
                "orders": [
                    {
                        "order_id": "123456",
                        "status": "completed",
                        "amount": 100.00
                    }
                ],
                "total": 1
            }
        }
        
        mock_session_instance = Mock()
        mock_session_instance.get.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        # 执行测试
        with patch('builtins.print') as mock_print:
            获取订单列表()
            
        # 验证请求参数
        mock_session_instance.get.assert_called_once()
        call_args = mock_session_instance.get.call_args
        
        # 验证URL
        self.assertEqual(call_args[1]['url'] if 'url' in call_args[1] else call_args[0][0], 
                        self.expected_url)
        
        # 验证参数
        actual_params = call_args[1]['params'] if 'params' in call_args[1] else call_args[0][1]
        self.assertEqual(actual_params, self.expected_params)
        
        # 验证headers包含必要字段
        actual_headers = call_args[1]['headers'] if 'headers' in call_args[1] else call_args[0][2]
        self.assertIn('authorization', actual_headers)
        self.assertIn('x-content-security', actual_headers)
        
        # 验证打印了响应
        mock_print.assert_called_once()
    
    @patch('requests.session')
    def test_获取订单列表_网络错误(self, mock_session):
        """测试网络错误的情况"""
        mock_session_instance = Mock()
        mock_session_instance.get.side_effect = requests.exceptions.RequestException("网络错误")
        mock_session.return_value = mock_session_instance
        
        # 执行测试，应该抛出异常
        with self.assertRaises(requests.exceptions.RequestException):
            获取订单列表()
    
    @patch('requests.session')
    def test_获取订单列表_API错误响应(self, mock_session):
        """测试API返回错误响应的情况"""
        mock_response = Mock()
        mock_response.json.return_value = {
            "code": 401,
            "message": "unauthorized",
            "data": None
        }
        
        mock_session_instance = Mock()
        mock_session_instance.get.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        # 执行测试
        with patch('builtins.print') as mock_print:
            获取订单列表()
            
        # 验证打印了错误响应
        mock_print.assert_called_once_with({
            "code": 401,
            "message": "unauthorized", 
            "data": None
        })
    
    @patch('requests.session')
    def test_获取订单列表_JSON解析错误(self, mock_session):
        """测试JSON解析错误的情况"""
        mock_response = Mock()
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        
        mock_session_instance = Mock()
        mock_session_instance.get.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        # 执行测试，应该抛出JSON解析异常
        with self.assertRaises(json.JSONDecodeError):
            获取订单列表()
    
    def test_请求参数格式(self):
        """测试请求参数的格式是否正确"""
        # 验证时间参数格式
        self.assertIsInstance(self.expected_params["time_type"], int)
        self.assertEqual(self.expected_params["time_type"], 1)
        
        # 验证分页参数
        self.assertIsInstance(self.expected_params["idx"], int)
        self.assertIsInstance(self.expected_params["size"], int)
        self.assertGreater(self.expected_params["size"], 0)
    
    @patch('requests.session')
    def test_请求头格式(self, mock_session):
        """测试请求头格式是否正确"""
        mock_response = Mock()
        mock_response.json.return_value = {"test": "data"}
        
        mock_session_instance = Mock()
        mock_session_instance.get.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        with patch('builtins.print'):
            获取订单列表()
        
        # 获取实际的headers
        call_args = mock_session_instance.get.call_args
        actual_headers = call_args[1]['headers'] if 'headers' in call_args[1] else call_args[0][2]
        
        # 验证必要的请求头
        required_headers = [
            'authorization',
            'x-content-security',
            'user-agent',
            'accept-encoding',
            'accept-language'
        ]
        
        for header in required_headers:
            self.assertIn(header, actual_headers, f"缺少必要的请求头: {header}")
        
        # 验证authorization格式（JWT token）
        auth_header = actual_headers['authorization']
        self.assertTrue(auth_header.startswith('eyJ'), "authorization应该是JWT token格式")
        
        # 验证x-content-security格式
        security_header = actual_headers['x-content-security']
        self.assertIn('key=', security_header, "x-content-security应该包含key参数")
        self.assertIn('secret=', security_header, "x-content-security应该包含secret参数")
        self.assertIn('signature=', security_header, "x-content-security应该包含signature参数")


def run_tests():
    """运行所有测试"""
    print("开始测试获取订单列表接口...")
    print("=" * 50)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestOrderList)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 50)
    if result.wasSuccessful():
        print("✅ 所有测试通过!")
    else:
        print("❌ 部分测试失败!")
        print(f"失败: {len(result.failures)}, 错误: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    # 运行测试
    success = run_tests()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    
    # 可选：实际调用函数进行手动测试（注释掉以避免真实API调用）
    print("\n如果要进行真实API测试，请取消下面的注释:")
    print("# 获取订单列表()")
