#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的获取订单列表接口
"""

import sys
import os
import json
import base64
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🧪 测试更新后的获取订单列表接口")
print("=" * 60)

def analyze_new_token():
    """分析新的JWT token"""
    print("🔍 分析新的JWT Token:")
    
    # 新的token (来自标头.txt)
    new_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ0ODE3OTYsImlhdCI6MTc1Mzg3Njk5NiwibmFtZSI6Ikx1Y2tlcl9ZYW4iLCJzaWQiOjY5NzQzNDUwMjg0MDM4OSwidHlwZSI6MSwidWlkIjo2OTc0MzQ1MDI4NDAzOTB9.lJed8-xhejWvCBzcfv6pqIhtMx4UCIUNzJdR2DL_BWU"
    
    try:
        # 解码JWT token
        parts = new_token.split('.')
        if len(parts) != 3:
            print("❌ JWT token格式不正确")
            return False
        
        # 解码payload
        payload_data = parts[1] + '=' * (4 - len(parts[1]) % 4)
        payload = json.loads(base64.urlsafe_b64decode(payload_data))
        
        print("📋 Token信息:")
        
        if 'iat' in payload:
            iat_time = datetime.fromtimestamp(payload['iat'])
            print(f"  - 签发时间: {iat_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if 'exp' in payload:
            exp_time = datetime.fromtimestamp(payload['exp'])
            current_time = datetime.now()
            print(f"  - 过期时间: {exp_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if current_time > exp_time:
                print("  ⚠️  Token已过期!")
                return False
            else:
                remaining = exp_time - current_time
                print(f"  ✅ Token有效，剩余时间: {remaining}")
        
        if 'name' in payload:
            print(f"  - 用户名: {payload['name']}")
        
        if 'sid' in payload:
            print(f"  - 会话ID: {payload['sid']}")
        
        if 'uid' in payload:
            print(f"  - 用户ID: {payload['uid']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token解析失败: {e}")
        return False

def compare_changes():
    """对比更新前后的变化"""
    print("\n📊 更新内容对比:")
    print("-" * 40)
    
    changes = [
        ("请求方法", "GET", "POST"),
        ("URL", "https://api.goofish.pro/api/order/pager", 
         "https://api.goofish.pro/api/order/pager?channel=1&version=3.54.31"),
        ("数据传输", "URL参数 (params)", "JSON数据体 (json)"),
        ("Authorization", "旧token (已过期)", "新token (来自标头.txt)"),
        ("Origin", "https://goofish.pro", "https://www.goofish.pro"),
        ("Referer", "https://goofish.pro/sale/order/all", 
         "https://www.goofish.pro/sale/order/all"),
        ("User-Agent", "Chrome/131.0.0.0", "Chrome/138.0.0.0"),
        ("x-content-security", "旧签名", "新签名 (来自标头.txt)"),
        ("Content-Type", "未设置", "application/json;charset=UTF-8"),
        ("Accept", "未设置", "application/json")
    ]
    
    for item, old, new in changes:
        print(f"🔄 {item}:")
        print(f"   旧: {old}")
        print(f"   新: {new}")
        print()

def test_updated_function():
    """测试更新后的函数"""
    print("🚀 测试更新后的API接口:")
    print("-" * 40)
    
    try:
        from request_utils import 获取订单列表
        print("✅ 成功导入更新后的函数")
        
        print("\n📡 调用更新后的API...")
        print("请求详情:")
        print("  - URL: https://api.goofish.pro/api/order/pager?channel=1&version=3.54.31")
        print("  - 方法: POST")
        print("  - 数据格式: JSON")
        print("  - 认证: 最新JWT token")
        
        print("\n" + "-" * 30)
        print("API响应:")
        print("-" * 30)
        
        # 调用更新后的函数
        获取订单列表()
        
        print("-" * 30)
        print("✅ API调用完成")
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 错误分析
        error_msg = str(e).lower()
        print("\n🔍 可能的原因:")
        
        if "401" in error_msg or "unauthorized" in error_msg:
            print("  - 认证失败，可能token仍然无效")
        elif "403" in error_msg or "forbidden" in error_msg:
            print("  - 权限不足或签名验证失败")
        elif "400" in error_msg or "bad request" in error_msg:
            print("  - 请求格式错误或参数不正确")
        elif "connection" in error_msg:
            print("  - 网络连接问题")
        elif "timeout" in error_msg:
            print("  - 请求超时")
        else:
            print("  - 其他未知错误")
        
        return False

def main():
    """主函数"""
    # 分析新token
    token_valid = analyze_new_token()
    
    # 显示更新内容
    compare_changes()
    
    # 测试更新后的函数
    print("🧪 开始测试更新后的接口...")
    success = test_updated_function()
    
    print("\n" + "=" * 60)
    print("📝 测试总结:")
    
    if token_valid:
        print("✅ 新Token有效")
    else:
        print("⚠️  新Token可能已过期")
    
    if success:
        print("✅ API接口更新成功，调用正常")
    else:
        print("⚠️  API调用遇到问题，可能需要进一步调试")
    
    print("\n🔧 更新完成的内容:")
    print("1. ✅ 请求方法: GET → POST")
    print("2. ✅ URL: 添加了 channel=1&version=3.54.31 参数")
    print("3. ✅ Authorization: 更新为最新token")
    print("4. ✅ x-content-security: 更新为最新签名")
    print("5. ✅ 请求头: 更新为最新的浏览器信息")
    print("6. ✅ 数据传输: 改为JSON格式")
    
    print("\n📋 建议:")
    if not success:
        print("- 如果仍然失败，可能需要从浏览器获取更新的token")
        print("- 检查网络连接和防火墙设置")
        print("- 确认API端点和参数格式")
    else:
        print("- 接口更新成功，可以正常使用")
        print("- 建议定期更新token以保持有效性")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
